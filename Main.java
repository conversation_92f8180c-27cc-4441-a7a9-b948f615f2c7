import java.util.concurrent.Semaphore;

public class Main {
    public static void main(String[] args) throws InterruptedException {
        SharedSemaphores semaphores = new SharedSemaphores();
        HEThread heThread = new HEThread(semaphores);
        LThread lThread = new LThread(semaphores);
        OThread oThread = new OThread(semaphores);
        heThread.start();
        lThread.start();
        oThread.start();
        Thread.sleep(100);

        heThread.interrupt();
        lThread.interrupt();
        oThread.interrupt();

        heThread.join();
        lThread.join();
        oThread.join();
        System.out.println();
        System.out.println("Goodbye");
    }
}

class SharedSemaphores {
    Semaphore waitO;
    Semaphore waitHE;
    Semaphore waitL;

    public SharedSemaphores() {
        this.waitHE = new Semaphore(0);
        this.waitO = new Semaphore(0);
        this.waitL = new Semaphore(0);
    }
}

class HEThread extends Thread {
    private SharedSemaphores semaphores;

    public HEThread(SharedSemaphores semaphores) {
        this.semaphores = semaphores;
    }

    @Override
    public void run() {
        while (true) {
            System.out.print("H");
            System.out.print("E ");
            semaphores.waitHE.release();
            semaphores.waitHE.release();
        }
    }
}

class LThread extends Thread {
    private SharedSemaphores semaphores;

    public LThread(SharedSemaphores semaphores) {
        this.semaphores = semaphores;
    }

    @Override
    public void run() {
        try {
            while (!Thread.interrupted()) {
                while (semaphores.waitHE.availablePermits() >= 1) {
                    this.semaphores.waitHE.acquire();
                    System.out.print("L");
                }
                this.semaphores.waitL.release();
            }
        } catch (InterruptedException e) {
        }
    }
}

class OThread extends Thread {
    private SharedSemaphores semaphores;

    public OThread(SharedSemaphores semaphores) {
        this.semaphores = semaphores;
    }

    @Override
    public void run() {
        try {
            while (!Thread.interrupted()) {
                this.semaphores.waitL.acquire();
                System.out.print("O ");
            }
        } catch (InterruptedException e) {
        }
    }
}